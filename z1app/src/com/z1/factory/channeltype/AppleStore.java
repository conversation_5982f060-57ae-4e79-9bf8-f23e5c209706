package com.z1.factory.channeltype;

import java.util.List;
import java.util.Map;

import z1.channel.def.CredentialsDef;

import com.z1.registration.def.UrlInfoType;
import com.z1social.apple.ApplePageSearch;

public class AppleStore implements IChannelType
{

  private enum channelInfo
  {
    channelType,
    companyName,
    competitors,
    industry,
    url
  }
  
  @Override
  public String getAccessToken()
  {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public String getFeedHandler()
  {
    return "com.z1social.channel.ITunesReviewCollector";
  }

  @Override
  public List<Map<String, Object>> getChannelInfoByCompanyName(
      Map<String, Object> channelInput)
  {
    return ApplePageSearch.fetchPageByName((String) channelInput.get(channelInfo.companyName.name()));
  }

  @Override
  public List<Map<String, Object>> getChannelInfoByUrl(
      Map<String, Object> channelInput)
  {    
    String id = (String) channelInput.get(channelInfo.url.name());
    return ApplePageSearch.fetchPageById(id);
  }

  @Override
  public void setCredentialDef(UrlInfoType channelUrl, CredentialsDef cd)
  {
    if (channelUrl.getAccessToken() != null
        && channelUrl.getAccessToken().trim().length() > 0)
    {
      cd.setAccessToken(channelUrl.getAccessToken());
    }

  }

}
