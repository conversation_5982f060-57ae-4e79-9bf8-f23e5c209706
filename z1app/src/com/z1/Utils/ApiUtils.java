package com.z1.Utils;

import org.apache.commons.lang3.StringUtils;
import udichi.core.*;

import udichi.core.cache.ObjectCache;
import udichi.core.data.Result;
import udichi.core.log.ULogger;
import udichi.core.util.JsonMarshaller;
import udichi.gateway.defservice.DefinitionItem;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.c3.SharedUrlService;
import z1.c3.api.Commons;
import z1.core.utils.Utils;
import z1.template.TemplateUtils.Z1VersionComparator;
import z1.users.User;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Utilities for public api calls.
 *
 */
public abstract class ApiUtils
{
  public static final String API_CHANNEL = "_z1_api_channel";
  public static final String MASTER_KEY_TYPE = "z1_t_master";
  public static final String SHARED_URL_CACHE ="_SharedURL_";
  private static final String ANDROID="android";
  private static final String HTML5="html5";
  private static final String IOS="ios";
  static Map<String, ApiKey> _apiKeys = new java.util.HashMap<>(1000);
  
  private static final String _VERSION_FILE_NAME = "META-INF/version.properties";
  private static Map<String, Object> sdkCompatInfo = new java.util.HashMap<>(10);
  private static String platformVersion;
  private static boolean skipPlatformCheck;
  private static final String CERTIFIED_SDK_VERSION = z1.commons.Utils
          .getPropertyValue("sdk.certified.ver", StringUtils.EMPTY).trim();
  private static final String MIN_SDK_VERSION = z1.commons.Utils
          .getPropertyValue("sdk.min.ver",StringUtils.EMPTY).trim();

  private static final Z1VersionComparator versionComparator = new Z1VersionComparator();

  static
  {
    try
    {
      platformVersion = "";

      InputStream inputStream = ApiUtils.class.getClassLoader()
          .getResourceAsStream(_VERSION_FILE_NAME);
      Properties prop = new Properties();
      if (inputStream != null)
      {
        prop.load(inputStream);
        String android = prop.getProperty(ANDROID);
        String ios = prop.getProperty(IOS);
        String html5 = prop.getProperty(HTML5);
        List<String> androidList = Arrays.asList(android.split(","));
        List<String> iosList = Arrays.asList(ios.split(","));
        List<String> html5List = Arrays.asList(html5.split(","));
        sdkCompatInfo.put(ANDROID, androidList);
        sdkCompatInfo.put(IOS, iosList);
        sdkCompatInfo.put(HTML5, html5List);
        skipPlatformCheck = Boolean
            .parseBoolean((String) prop.get("skipPlatformCheck"));

        String v = prop.get("platformVersion").toString();

        // Possible platform tag patterns are:
        // Minor-4.202.4
        // Patch-*********
        // z1-release-4.191.1
        // Z1-QA-Build
        if (v.contains("-"))
        {
          v = v.substring(v.lastIndexOf("-") + 1);
        }

        // Skip version check if 'platformVersion' does not begin with a
        // number and not contain atleast one '.'
        // Also check if 'platformVersion' was not updated at build time.
        if (!v
            .matches("^\\d+[.].*") /* || v.equals("${z1.platform.version}") */)
        {
          skipPlatformCheck = true;
          UContext sysCtx = UContext.getSystemCoreInstance();
          ULogger logger = sysCtx.getLogger(ApiUtils.class);
          logger.warning(
                  "System will not check for for platform version on SDK connect."
                          + " Platform version must start with '<number>.'"
                          + " Failed to identify: " + v);
        }
        else
        {
          platformVersion = v;
        }

      }
      else
      {
        System.out.println("!!!!!!!!!!!!!!!!!!!!!!!");
        System.out.println("!!!!! Failed to load: " + _VERSION_FILE_NAME);
        System.out.println("!!!!!!!!!!!!!!!!!!!!!!!");
      }

    }
    catch (Exception e)
    {
      System.out
          .println("Error initializing ApiUtils class: " + e.getMessage());
    }
  }

  /**
   * Returns false if SDK obsolete or version greater than platformVersion.
   * platformVersion is set through 'META-INF/version.json'
   * 
   * !!-:NOTE:-!! Since 'sdkVersion' in connect is sent for new sdks only, it is
   * expected that version scheme matches 'x.maj.min.patch' scheme. Otherwise
   * _isSdkObsolete() will cause this method to return false.
   * 
   * Ex. SDK 4.19x.x.x will be incompatible with Platform 4.21x.x.x.
   * 
   * @param sdkVersion
   * @return
   * @throws Exception
   */
  public static boolean isSDKSupportedByPlatform(String os, String sdkVersion)
      throws Exception
  {
    if (skipPlatformCheck) return true;

    return !ApiUtils.isSdkBlackListedOrObsolete(os, sdkVersion)
        && versionComparator.compare(sdkVersion,
            platformVersion) <= 0;
  }

  /**
   * Returns true if SDK is blacklisted under 'version.json' or older than
   * certified and min sdk version
   * 
   * @param os
   * @param sdkVersion
   * @return
   */
  public static boolean isSdkBlackListedOrObsolete(String os, String sdkVersion)
      throws Exception
  {
    // Return immediately if blacklisted.
    if (_isSDKVersionBlackListed(os, sdkVersion)) return true;

    String[] parts = sdkVersion.split("\\.");
    int arch = Integer.parseInt(parts[0]);

    // Blacklist immediately if below arch 4
    if (arch < 4) return true;

    if (CERTIFIED_SDK_VERSION.isEmpty())
    {
      throw new Exception("sdk.certified.ver not found in zineone.properties.");
    }

    // pick smaller of the two version values.
    String minVer = MIN_SDK_VERSION.isEmpty()
        || versionComparator.compare(CERTIFIED_SDK_VERSION, MIN_SDK_VERSION)
        < 0 ? CERTIFIED_SDK_VERSION : MIN_SDK_VERSION;

    return versionComparator.compare(sdkVersion, minVer) < 0;
  }

  /**
   * Returns true if SDK version is listed under blocklist in
   * 'META-INF/version.json'
   * 
   * @param os
   * @param sdkVersion
   * @return
   */
  @SuppressWarnings("unchecked")
  private static boolean _isSDKVersionBlackListed(String os, String sdkVersion)
  {
    // Block SDK if payload is missing 'os' or 'sdkVersion'.
    if (StringUtils.isEmpty(os) || StringUtils.isEmpty(sdkVersion)) return true;

    List<String> blockedList = (List<String>) sdkCompatInfo.get(os);

    return blockedList != null && blockedList.contains(sdkVersion);
  }
  
  private static final String[] IP_HEADER_CANDIDATES = { "X-Forwarded-For",
      "Proxy-Client-IP", "WL-Proxy-Client-IP", "HTTP_X_FORWARDED_FOR",
      "HTTP_X_FORWARDED", "HTTP_X_CLUSTER_CLIENT_IP", "HTTP_CLIENT_IP",
      "HTTP_FORWARDED_FOR", "HTTP_FORWARDED", "HTTP_VIA", "REMOTE_ADDR" };
  
  /**
   * Returns client's ip address from {@link ApiUtils#IP_HEADER_CANDIDATES} in
   * HttpServletRequest.
   * 
   * @param req
   * @return
   */
  public static String getCustomerIpAddress(HttpServletRequest req)
  {
    for (String header : IP_HEADER_CANDIDATES)
    {
      String ip = req.getHeader(header);
      if (ip != null && !ip.trim().isEmpty() && !"unknown".equalsIgnoreCase(ip))
      {
        return ip;
      }
    }
    return req.getRemoteAddr();
  }


  /**
   * Updates a context namespace from a given apikey.
   * 
   * @param ctx
   * @throws Exception
   */
  public static void updateContextNamespace(UContext ctx, ApiKey apiKey)
      throws Exception
  {
    // Get the associated namespce to set to the context
    String ns = (String) apiKey.getValues().get(ApiKey.Fields.namespace.name());
    if (ns == null)
    {
      throw new Exception("Invalid api key, no namespace found.");
    }
    ctx.setNamespace(ns);
    ctx.put(Commons.ReqFields.apiKey.name(), apiKey.getId(), true);
    String channelId = (String) apiKey.getValues().get(ApiKey.Fields.channel.name());
    if (channelId == null) channelId = MASTER_KEY_TYPE;
    ctx.put(API_CHANNEL, channelId,
        true);
  }

  public static ApiKey getApiKey(UContext ctx, HttpServletRequest req)
      throws Exception
  {
    return getApiKey(ctx, req, true);
  }

  /**
   * Gets the apikey from a http request. The apikey can be part of the http
   * header or passed as http request parameter.
   * If apiKey does not exist look for access token and retreive the apiKey
   * 
   * @param ctx
   * @param req
   * @return apiKey
   * @throws Exception
   */
  public static ApiKey getApiKey(UContext ctx, HttpServletRequest req,
      boolean raiseExceptionIfKeyInactive) throws Exception
  {
    String key = null;
    try
    {
      key = App.apiKeyExtractor.get(ctx, req);
    }
    catch (UException e)
    {
      throw new Exception(e.getMessage());
    }

    // No api key, we will return null
    if (key == null) return null;

    ApiKey apiKey = ApiKey.get(ctx, key);
    if (apiKey != null && !apiKey.isActive() && raiseExceptionIfKeyInactive)
    {
        ULogger logger = ctx.getLogger(ApiUtils.class);
        if (logger.canLog()) logger.log(String.format("Api Key %s is inactive.", key));
        throw new Exception(z1.commons.Const.INACTIVE_APIKEY);
    }
    
//    ApiKey apiKey = _apiKeys.get(key);
//    if (apiKey != null) return apiKey;
//    
//    // If api key is not found, we will load this thread safe.
//    // This will block all requests as we don't know the namespace yet.
//    synchronized (_lock)
//    {
//      // check again - double check pattern
//      apiKey = _apiKeys.get(key);
//      if (apiKey == null)
//      {      
//        apiKey = ApiKey.load(ctx, key);
//        if (apiKey == null)
//        {
//          throw new Exception("Invalid API Key:" + key);
//        }
//        _apiKeys.put(key, apiKey);
//      }
//    }

    return apiKey;
  }

  // ...............................................................
  /**
   * Checks if the current context is valid for the given device information.
   * 
   * @param deviceType
   * @param deviceOS
   * @return Returns true if the current context has the device information
   * that matches to the given data.
   */
  public static boolean isValidContext(UContext ctx, String deviceType,
      String deviceOS)
  {
    // Create a channel ID from the device type and OS info
    String channelId = null;
    if (deviceType != null && deviceOS != null)
    {
      channelId = deviceType + "|" + deviceOS;
    }
    
    return isValidContext(ctx, channelId);
  }

  // ...............................................................
  /**
   * Checks if the current context is valid for the given channel id.
   * 
   * @param channelId
   * @return Returns true if the current context has the channel information
   * that matches to the given channel ID.
   */
  public static boolean isValidContext(UContext ctx, String channelId)
  {
    // If the context has a channel ID and it matches, we'll return true
    String apiKeyChannel = (String) ctx.get(API_CHANNEL);
    if (apiKeyChannel == null) return false;
    if (apiKeyChannel.equalsIgnoreCase(MASTER_KEY_TYPE)) return true;
    if (channelId == null) return false;
    return channelId.equalsIgnoreCase(apiKeyChannel);
  }
  
  /**
   * Checks if a caller have rights to process 
   * Specifically verify if it is a "public" endpoint 
   * or  ApiKey is provided or user is logged in, so namespace is set
   * 
   * @param req
   * @param ctx
   * @param pathParts
   * @return <code>true</code> if access is permitted
   * @throws Exception 
   */
  public static boolean
  checkAccess(HttpServletRequest req, UContext ctx, String[] pathParts) throws Exception
  {
    boolean isPublic = checkIfPublic(ctx, pathParts, req);
    if ( isPublic ) {
      return true; // public url, no need to check more
    }
    // Get the api key from the request
    ApiKey key = getApiKey(ctx, req);
    if (key != null)
    {
      ApiUtils.updateContextNamespace(ctx, key);
    }
    else
    {
      // Is this coming from C3?? If yes, there should be a namespace
      // set to the context already
      String ns = ctx.getNamespace();
      if (ns == null || ns.trim().length() == 0)
      {
        return false;
      }
    }
    return true;
  }

  /**
   * Update the namespace for context from apikey
   * 
   * @param ctx
   * @param req
   */
  public static void updateContextNamespaceFromApiKey(UContext ctx,
      HttpServletRequest req)
  {
    ULogger logger = ctx.getLogger(ApiUtils.class);
    try
    {
      // Get the api key from the request
      ApiKey key = getApiKey(ctx, req, false);
      if (key != null)
      {
        ApiUtils.updateContextNamespace(ctx, key);
      }
    }
    catch (Exception e)
    {
      logger.severe("Fail to set the context namespace.", e);
    }
  }

  /**
   * verify if it is a "public" endpoint 
   * 
   * @param uctx
   * @param pathParts
   * @param request
   * @return <code>True</code> if it is "public" one
   */
  public static boolean checkIfPublic (UContext uctx, String[] pathParts, HttpServletRequest request) {
    // we are expecting // [kbdoc, <docid>] or [service, myService]
    if (pathParts.length < 2) {
      return false;
    }
    String type = pathParts[0];
    String itemId = pathParts[1];
    String itemType;
    if (type.equals("kbdoc")) { 
      itemType = "content";
    }
    else if (type.equals("service")) {
      itemType = "microservice";
    } else if (type.equals("images"))
    {
      return true;
    }
    else {
      return false;
    }
    String urlNS = request.getParameter("namespace");
    if (urlNS == null) {
      return false;
    }
    ObjectCache oc = ObjectCache.getInstace(UContext.getInstance().setNamespace(Const.NS_UDICHI_CORE));
    String key = _getCacheKey(itemId, itemType, urlNS);
    Map <String, Boolean> cachedMap = oc.get(SHARED_URL_CACHE);
    if (cachedMap == null) {
      cachedMap = new HashMap <> ();
    }
    Boolean isPublic  =  cachedMap.get(key);
    if (isPublic != null)
    {
      // found cached value 
      if (isPublic)
      {
        _setNamespaceIfUnknown(uctx, urlNS);
        return true;
      }
      else
      {
        return false;
      }
    }
    // didn't found in cache, have to call service and put value in the cache 
    SharedUrlService ss = new SharedUrlService(uctx);
    isPublic = ss.getSharedUrlByIdTypeAndNamespace(itemId, itemType, urlNS) != null;
    cachedMap.put(key, isPublic);
    oc.put(SHARED_URL_CACHE, cachedMap);
    if (isPublic) {
      _setNamespaceIfUnknown (uctx, urlNS);
      return true;
    }
    return false;

  }
  
  //...............................................................
  /**
   * get the corresponding user name from a given email address
   * return "Unknown" if we can't find the it
   * @param uctx
   * @param email
   * @return name
   */
   public static String userNameFromEmail(final UContext uctx, Object email) {
     User user = (email != null) ? User.load(uctx, (String) email) : null;
     String name = "Unknown";

     if (user != null)
     {
       String fName = Optional
           .ofNullable((String) user.getValues().get(User.Fields.fname.name()))
           .orElse("Unknown");
       String lName = Optional
           .ofNullable((String) user.getValues().get(User.Fields.lname.name()))
           .orElse("");
       name = (lName.isEmpty()) ? fName : fName + " " + lName;
     }
     return name;
   }
   //...............................................................
   /**
    * check the uniqueness of a proposed cube name
    * @param uctx
    * @param name
    * @return <code>True</code> if it is name hasn't been used by existing cube
    */
   public static boolean isValidCubeName(final UContext uctx, String name) {
     Result<DefinitionItem> items = DefinitionItem.getAllItems(uctx,
         ArtifactType.cube);
     String existingName = null;
     for (DefinitionItem di : items.getData()) {
       if (di.getValues().get("name")==null)
         continue;
       existingName = (String)di.getValues().get("name");
       if (name.equals(existingName)) {
         return false;
       }
     }
     return true;
   }
   //...............................................................
   /**
    * check the uniqueness of a proposed cubequery name
    * @param uctx
    * @param name
    * @return <code>True</code> if it is name hasn't been used by existing cube
    */

   public static boolean isValidCubeQueryName(final UContext uctx, String name) {
     List<CustomConfig> configs = CustomConfig.loadAll(uctx,
         Type.cubeQuery);
     for(CustomConfig config: configs) {
       if (config == null || config.getId()==null) continue;
       if (name.equals(config.getId())) {
         return false;
       }
     }
     return true;
   }
   
   //...............................................................
   
   /**
    * given a cube id, find all cube queries associated with it
    * @param cubeId   
    * @param uctx
    * @return cubeQueries
    */
   public static List<Map<String, Object>> getQueriesForTheCube(String cubeId,
       final UContext uctx){
     List<CustomConfig> configs = CustomConfig.loadAll(uctx,
         Type.cubeQuery);
     if (configs.isEmpty())
     {
       return new LinkedList<>();
     }
     Map<String, Object> payload = null;
     Map<String, Object> cubeBrief = null;
     List<Map<String, Object>> cubeQueries = new LinkedList<>();
     for (CustomConfig config : configs)
     {
       if (config == null) continue;
       payload = new JsonMarshaller().readAsMap(config.getPayload());
       if (payload == null) continue;
       String cubeName = (String) payload.get("cube");
       if (cubeName.equalsIgnoreCase(cubeId))
       {
         cubeBrief = new LinkedHashMap<>();
         cubeBrief.put("name", config.getId());
         cubeBrief.put("lastUpdatedOn", config.getLastUpdatedTime());
         cubeBrief.put("lastUpdatedBy",
             ApiUtils.userNameFromEmail(uctx, config.getLastUpdatedBy()));
         cubeBrief.put("payload", config.getPayload());
         cubeQueries.add(cubeBrief);
       }
     }
     return cubeQueries;
   }

  /**
   * ZMOB-19077: Support toggling between Daily and Hourly granularity
   * @param analyticQueryMap
   * @param fromDate
   * @param toDate
   * @param periodTime
   * @param metric
   * @return
   *   the result list containing metrics for each hour
   */
   public static List<Map<String, Object>> postProcessingQueryResultPerHour(
           List<Map<String, Object>> analyticQueryMap,
           String fromDate,
           String toDate,
           String periodTime, String metric) {
     if (analyticQueryMap == null || fromDate == null || toDate ==null
             || periodTime == null) {
       return analyticQueryMap;
     }
     Integer period = Integer.valueOf(periodTime);
     try {
       Map<String, Object> week1Data = analyticQueryMap.get(1);
       Map<String, Object> week2Data = analyticQueryMap.get(0);
       if (week1Data.isEmpty() && week2Data.isEmpty())
         return analyticQueryMap;

       Calendar calendar = Calendar.getInstance();
       Calendar calendar2 = Calendar.getInstance();
       if (fromDate.length()==8) fromDate = fromDate + "00";
       if (toDate.length()==8) toDate = toDate + "00";
       Date f = new SimpleDateFormat("yyyyMMddHH", Locale.ENGLISH).parse(fromDate);
       Date t = new SimpleDateFormat("yyyyMMddHH", Locale.ENGLISH).parse(toDate);
       calendar.setTime(f);
       Set<String> validDateForWeek1 = new HashSet<>(), validDateForWeek2 = new HashSet<>();
       boolean thisWeekFirst = false;
       for(Map day: (ArrayList<Map>)week1Data.get("values")){
         String timeStamp = (String)day.get("date");
         // some hack to handle old data
         if (timeStamp.length()==8) timeStamp = timeStamp + "00";
         Date tempDate = new SimpleDateFormat("yyyyMMddHH", Locale.ENGLISH).parse(timeStamp);
         if (tempDate.before(f) || tempDate.after(t))
           thisWeekFirst = true;
         validDateForWeek1.add((String)day.get("date"));
       }

       for(Map day: (ArrayList<Map>)week2Data.get("values")){
         String timeStamp = (String)day.get("date");
         // some hack to handle old data
         if (timeStamp.length()==8) timeStamp = timeStamp + "00";
         Date tempDate = new SimpleDateFormat("yyyyMMddHH", Locale.ENGLISH).parse(timeStamp);
         if (tempDate.after(f) && tempDate.before(t))
           thisWeekFirst = true;
         validDateForWeek2.add(timeStamp);
       }

       if (thisWeekFirst) {
         Map<String, Object> temp = week2Data;
         week2Data = week1Data;
         week1Data = temp;
         Set<String> temp2 = validDateForWeek2;
         validDateForWeek2 = validDateForWeek1;
         validDateForWeek1 = temp2;
       }

       for(int i = 0; calendar.getTime().before(t); i++){
         calendar.setTime(f);
         calendar.add(Calendar.HOUR_OF_DAY, i);
         String dayOfWeek1 = new SimpleDateFormat("yyyyMMddHH").format(calendar.getTime());

         calendar2.setTime(f);
         calendar2.add(Calendar.DATE, -period);
         calendar2.add(Calendar.HOUR_OF_DAY, i);
         String dayOfWeek2 = new SimpleDateFormat("yyyyMMddHH").format(calendar2.getTime());

         if (validDateForWeek1.contains(dayOfWeek1) && !validDateForWeek2.contains(dayOfWeek2)){
           LinkedHashMap<String, Object> temp = new LinkedHashMap<>();
           temp.put("date", dayOfWeek2);
           temp.put(metric, 0);
           ((ArrayList) week2Data.get("values")).add(temp);
         } else if (!validDateForWeek1.contains(dayOfWeek1) && validDateForWeek2.contains(dayOfWeek2)){
           LinkedHashMap<String, Object> temp = new LinkedHashMap<>();
           temp.put("date", dayOfWeek1);
           temp.put(metric, 0);
           ((ArrayList) week1Data.get("values")).add(temp);
         }
       }
     } catch (Exception e){
       z1.commons.Utils.showStackTraceIfEnable(e, null);
     }
     return analyticQueryMap;
   }

  private static String _getCacheKey(String itemId, String itemType,  String urlNS)
  {
    return   itemType + "|" + itemId +  "|" +  urlNS;
  }
  
  private static void _setNamespaceIfUnknown (UContext uctx, String urlNS) {
    if (uctx.getNamespace() == null ) {
      uctx.setNamespace(Utils.generateNameSpaceFromDomainName(urlNS));
    }
  }
  
  // ...............................................................

  /**
   * Returns current platform version in 'Arch.Major.Minor' form
   * 
   * @return
   */
  public static String getPlatformVersion()
  {
    if (platformVersion == null || platformVersion.isEmpty()) return null;

    List<String> list = new ArrayList<>(
        Arrays.asList(platformVersion.split("\\.")));

    // Check if minor is not a number, eg: 4.211.beta1 etc.
    // If yes we replace minor with 0.
    if (list.size() > 2 && !list.get(2).matches("[0-9]+"))
    {
      String min = list.get(2).replaceAll("\\D.*", "");
      list.add(2, min.length() == 0 ? "0" : min);
    }

    // Only return first 3 parts to match Arch.Major.Minor
    return list.stream().limit(3).collect(Collectors.joining("."));
  }


}
